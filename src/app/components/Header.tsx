import React from "react";
import Logo from "@/assets/images/logo.png";
import Flag from "@/assets/images/flag.png";
import Image from "next/image";
import ArrowDown from "@/icons/ArrowDown.svg";

function Header() {
  return (
    <header className="container max-w-[1600px] !w-auto mx-5 xl:mx-auto bg-white h-18  md:h-24 rounded-full flex justify-between items-center mt-6 md:mt-16.5 px-8">
      <div className="hidden md:block"></div>
      <div className="">
        <Image src={Logo} alt="logo" width={127} className="w-24 md:w-32" />
      </div>

      <div className="flex items-center gap-2.5">
        <Image src={Flag} alt="flag" width={36} className="w-7 md:w-9" />
        <ArrowDown />
      </div>
    </header>
  );
}

export default Header;
