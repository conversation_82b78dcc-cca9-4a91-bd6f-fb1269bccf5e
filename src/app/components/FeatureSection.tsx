import Button from "@/components/Button";
import React from "react";
import JoinImage from "@/assets/images/join.png";
import HandImage from "@/assets/images/two-hand.png";
import UnityImage from "@/assets/images/unity-hand.png";
import MeditationImage from "@/assets/images/meditation.png";
import FeatureCard from "./FeatureCard";

function FeatureSection() {
  return (
    <section className="relative overflow-x-clip">
      <div
        className="bg-foreground absolute w-[170%] h-[121%]  md:h-[140%] -z-10"
        style={{
          top: -90,
          transform: "skew(0,-12deg)",
        }}
      ></div>
      <div className="flex items-center container mx-auto pt-6  md:pt-42 mt-38 md:mt-64 justify-between flex-col md:flex-row px-5 md:px-0">
        <div className="flex-1 w-auto md:max-w-xl">
          <p className="text-xl text-primary">
            Quisque porttitor vitae vel amet neque scelerisque mattis.
            Consectetur nibh velit magna consectetur leo.
          </p>
          <h3 className="font-bold text-4xl md:text-[50px] text-white mt-3.5">
            Cursus Integer Conseq Aliquam Tristique.
          </h3>
          <Button variant="white" className="mt-8.5 md:mt-15 w-full md:w-auto">
            Lorem Ipsum
          </Button>
        </div>
        <div className="flex-1 flex  items-start gap-15 justify-end flex-col md:flex-row w-full md:w-auto">
          <div className="grid gap-7.5 md:gap-15 mt-14 md:mt-0 w-full md:w-auto">
            <FeatureCard
              image={JoinImage}
              index={1}
              title="Phasellus Vitae"
              subtitle="Quisque"
              description="Porttitor vitae vel amet"
              className=""
            />
            <FeatureCard
              image={UnityImage}
              index={3}
              title="Eleifend Pulvinar "
              subtitle="Vitae"
              description="Consectetur nibh velit"
              yellow
              className="hidden md:flex"
            />
            <FeatureCard
              image={HandImage}
              index={2}
              title="Iaculis Magna"
              subtitle="Porttitor"
              description="neque scelerisque mattis."
              className="flex md:hidden"
              yellow
            />

            <FeatureCard
              image={UnityImage}
              index={3}
              title="Eleifend Pulvinar "
              subtitle="Vitae"
              description="Consectetur nibh velit"
              className="flex md:hidden"
            />
            <FeatureCard
              image={MeditationImage}
              index={4}
              title="Velit Odio Phir"
              subtitle="Ametneq"
              description="magna consectetur leo."
              className="flex md:hidden"
              yellow
            />
          </div>

          <div className="gap-15 mt-32 hidden md:grid">
            <FeatureCard
              image={HandImage}
              index={2}
              title="Iaculis Magna"
              subtitle="Porttitor"
              description="neque scelerisque mattis."
              yellow
            />
            <FeatureCard
              image={MeditationImage}
              index={4}
              title="Velit Odio Phir"
              subtitle="Ametneq"
              description="magna consectetur leo."
              className=""
            />
          </div>
        </div>
      </div>
    </section>
  );
}

export default FeatureSection;
