import React from "react";
import UserImage from "@/assets/images/user.png";
import Image from "next/image";
import ArrowLeft from "@/assets/icons/ArrowLeftLean.svg";
import ArrowRight from "@/assets/icons/ArrowRightLean.svg";

function TestimonialSection() {
  return (
    <div className="bg-white rounded-[57px] !w-auto mx-5 md:rounded-[100px] container flex-col md:flex-row testimonial-container group hover:text-whitev px-7.5 py-10 md:px-34 md:py-25 md:mx-auto max-w-[1650px] relative flex items-center md:gap-17.5 gap-10 overflow-clip">
      <div className="testimonial-overlay absolute inset-0 w-full h-full bg-foreground/80 rounded-[100px]">
        <Image
          src={UserImage}
          alt="User Image"
          className="w-full h-full object-cover"
        />

        <div className="absolute inset-0 w-full h-full bg-foreground/80 md:rounded-[100px] rounded-[57px]"></div>
      </div>
      <div className="relative">
        <Image
          src={UserImage}
          alt="User Image"
          width={500}
          height={500}
          className="md:w-119 md:h-119 rounded-full md:min-w-max w-full h-full"
        />
      </div>

      <div className="relative text-center md:text-left">
        <h2 className="text-3xl md:text-5xl font-bold group-hover:text-white">
          What our customers thought?
        </h2>
        <p className="md:mt-10 mt-5 text-base md:text-3xl text-foreground group-hover:text-white ">
          Euismod magna id purus eget nunc ligula suspendisse dui netus.
          Condimentum blandit rutrum at mauris enim pulvinar duis etiam duis.
        </p>
        <h4 className="mt-5 text-2xl font-bold group-hover:text-primary">
          Holly Davidson
        </h4>

        <div className="flex gap-10 mt-7.5 md:mt-10 justify-center md:justify-start">
          <button>
            <ArrowLeft className="fill-foreground group-hover:fill-white" />
          </button>
          <button>
            <ArrowRight />
          </button>
        </div>
      </div>
    </div>
  );
}

export default TestimonialSection;
