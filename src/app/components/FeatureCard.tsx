import { cn } from "@/utils/class-util";
import Image, { StaticImageData } from "next/image";
import React from "react";

function FeatureCard({
  description,
  image,
  index,
  subtitle,
  title,
  yellow = false,
  className,
}: {
  className?: string;
  index: number;
  title: string;
  subtitle: string;
  description: string;
  image: StaticImageData;
  yellow?: boolean;
}) {
  return (
    <div
      className={cn(
        "card-background-animation shadow-[0_0px_25px_24px_rgb(0,0,0,0.1)] relative p-px overflow-clip rounded-[60px]",
        className
      )}
    >
      <div
        className={cn(
          "p-12.5 bg-white  rounded-[60px] w-full md:w-87.5 group ",
          yellow && "bg-primary"
        )}
      >
        <div className="relative z-10 duration-150`">
          <Image
            src={image}
            alt="feature"
            width={80}
            height={80}
            className="w-12.5 h-12.5 relative"
          />
          <div className="flex gap-1.5 items-end relative group-hover:text-white">
            <span className="font-bold text-7xl mt-8 block">{index}</span>
            <span
              className={cn(
                "w-3 h-3 bg-primary block rounded-full mb-2.5 ",
                yellow && "bg-white",
                "group-hover:text-white"
              )}
            ></span>
          </div>
          <div className="relative group-hover:text-white">
            <h3 className="font-bold text-2xl mt-3 ">{title}</h3>
            <h5 className="font-semibold text-xl mt-7">{subtitle}</h5>
            <p className="font-light text-lg mt-0.5">{description}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FeatureCard;
