import Badge from "@/components/Badge";
import Button from "@/components/Button";
import Image from "next/image";
import React from "react";
import Illustration from "@/assets/images/Illustration-2.png";

function InfoSection() {
  return (
    <section className="flex gap-5 md:gap-10 mx-5 !w-auto container md:mx-auto mt-15 md:mt-45 pb-24 md:pb-40 items-center flex-col-reverse md:flex-row">
      <div className="flex-1 ">
        <h2 className="text-4xl md:text-5xl font-bold">
          Cursus Integer <br /> consequat Tristique.
        </h2>
        <div className="mt-7.5 md:mt-15 flex flex-wrap gap-3.5">
          <Badge>Ac viverra sed risus praesent vulputate.</Badge>
          <Badge>Natoqu consectetur pulvinar.</Badge>
          <Badge>Sollicitudin ornare tempus nulla varius pulvinar.</Badge>
          <Badge>Varius pulvinar</Badge>
          <Badge>Natoque id tellus consectetur</Badge>
          <Badge>Vulputate et vulputate suspendisse</Badge>
        </div>

        <Button variant="black" className="mt-10 md:mt-15 text-white fill-white w-full md:w-auto">
          Lorem Ipsum
        </Button>
      </div>
      <div className="flex-1">
        <Image src={Illustration} alt="Illustration" className="-mt-4" />
      </div>
    </section>
  );
}

export default InfoSection;
