import Badge from "@/components/Badge";
import Button from "@/components/Button";
import Image from "next/image";
import React from "react";
import illustration from "@/assets/images/illustration-1.png";
import TextReveal from "@/components/TextReveal";

function HeroSection() {
  return (
    <section className="container mx-auto flex mt-10 md:mt-29 flex-col-reverse md:flex-row gap-10 md:gap-0 px-5">
      <div className="flex-1">
        <div className="">
          <h3 className="text-base md:text-3xl uppercase font-bold text-primary">
            risus praesent vulputate.
          </h3>
          <h1 className="text-4xl md:text-[80px] font-bold min-w-max md:leading-22">
            <span>Cursus Integer</span> <br />
            <span>
              Consequat{" "}
              <TextReveal className="before:w-[3px] before:h-9 md:before:w-[7px] md:before:h-22.5 ">
                Tristique.
              </TextReveal>
            </span>
          </h1>
        </div>
        <div className="flex flex-wrap gap-3.5 mt-15">
          <Badge>Cursus Integer</Badge>
          <Badge>Integer Consequat</Badge>
          <Badge>Tellus Euismod Pellentesque</Badge>
          <Badge>Aliquot Tristique</Badge>
          <Badge>Pellentesque Tempus</Badge>
          <Badge>Mauris Fermentum Praesent</Badge>
        </div>
        <Button className="mt-10 md:mt-15 w-full md:w-auto">Lorem Ipsum</Button>
      </div>
      <div className="flex-1">
        <Image
          src={illustration}
          alt="hero"
          width={800}
          height={800}
          className="w-full"
        />
      </div>
    </section>
  );
}

export default HeroSection;
