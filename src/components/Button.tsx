import React from "react";
import ArrowRight from "@/icons/ArrowRightBig.svg";
import { ClassValue } from "clsx";
import { cn } from "@/utils/class-util";

function Button({
  children,
  className,
  variant = "yellow",
}: {
  children: React.ReactNode;
  className?: ClassValue;
  variant?: "yellow" | "white" | "black";
}) {
  return (
    <button
      className={cn(
        "px-10 md:px-12.5 rounded-full flex items-center justify-between gap-18 bg-primary relative duration-300 h-15 md:h-19.5 bounce-rotate-animation border-transparent border-2 hover:border-black hover:before:bg-black button-animation",
        variant === "white" &&
          "bg-white hover:border-primary hover:before:bg-primary",
        variant === "black" &&
          "bg-black hover:border-white hover:before:bg-white",
        className
      )}
    >
      <span className="font-bold text-xl md:text-2xl">{children}</span>
      <ArrowRight
        className={cn(
          "bouncing-arrow fill-black",
          variant === "black" && "fill-white"
        )}
      />
    </button>
  );
}

export default Button;
