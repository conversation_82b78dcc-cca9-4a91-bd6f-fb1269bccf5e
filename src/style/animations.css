@keyframes rotate-and-bounce-down {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(115deg);
    }

    70% {
        transform: rotate(80deg);
    }

    90% {
        transform: rotate(95deg);
    }

    100% {
        transform: rotate(90deg);
    }
}

@keyframes rotate-and-bounce-up {
    0% {
        transform: rotate(90deg);
    }

    50% {
        transform: rotate(-20deg);
    }

    70% {
        transform: rotate(10deg);
    }

    90% {
        transform: rotate(-5deg);
    }

    100% {
        transform: rotate(0deg);
    }
}



.bouncing-arrow {
    animation: rotate-and-bounce-up 0.4s forwards ease-in;
}

.bounce-rotate-animation:hover .bouncing-arrow {
    animation: rotate-and-bounce-down 0.4s forwards ease-out;
}

.card-background-animation {
    position: relative;
    overflow: clip;
    cursor: pointer;
}

.card-background-animation:hover {
    color: white;
}

.card-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    text-align: center;
}

.card-background-animation::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--foreground);
    border-radius: 100%;
    z-index: 1;
    left: 50%;
    top: 100%;
    transform: translateY(50%) translateX(-50%) scale(1);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    padding: 10px;
    transition: none;
}


@keyframes background-double-bounce-up {
    0% {
        transform: translateY(0%) translateX(-50%) scale(1);
    }

    35% {
        transform: translateY(-105%) translateX(-50%) scale(1.1);
    }

    54% {
        transform: translateY(-60%) translateX(-50%) scale(1.2);
    }

    80% {
        transform: translateY(-102%) translateX(-50%) scale(1.3);
    }

    90% {
        transform: translateY(-94%) translateX(-50%) scale(1.3);
    }

    100% {
        transform: translateY(-100%) translateX(-50%) scale(1.3);
    }
}

@keyframes background-double-bounce-down {
    0% {
        transform: translateY(-100%) translateX(-50%) scale(1.3);
    }



    50% {
        transform: translateY(-10%) translateX(-50%) scale(1);
    }

    65% {
        transform: translateY(-25%) translateX(-50%) scale(1);
    }

    85% {
        transform: translateY(0%) translateX(-50%) scale(1);
    }

    95% {
        transform: translateY(-10%) translateX(-50%) scale(1);
    }

    100% {
        transform: translateY(0%) translateX(-50%) scale(1);
    }
}

.card-background-animation:hover::before {
    animation: background-double-bounce-up 0.6s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card-background-animation:not(:hover)::before {
    animation: background-double-bounce-down 0.6s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes triple-bounce-up {
    0% {
        transform: translateY(100%);
    }

    25% {
        transform: translateY(-10%);
    }

    45% {
        transform: translateY(5%);
    }

    65% {
        transform: translateY(-3%);
    }

    85% {
        transform: translateY(1%);
    }

    100% {
        transform: translateY(0%);
    }
}

@keyframes triple-bounce-down {
    0% {
        transform: translateY(0%);
    }

    60% {
        transform: translateY(95%);
    }

    75% {
        transform: translateY(70%);
    }

    100% {
        transform: translateY(100%);
    }
}

.testimonial-overlay {
    transform: translateY(100%);
    transition: none;
}

.testimonial-container:hover .testimonial-overlay {
    animation: triple-bounce-up 0.8s forwards cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.testimonial-container:not(:hover) .testimonial-overlay {
    animation: triple-bounce-down 0.6s forwards ease-out;
}

.button-animation::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 999;
    z-index: -1;
    transition: all 0.3s ease;
    inset: 0;
    opacity: 0;
}

.button-animation:hover::before {
    transform: translateY(14%) translateX(2%);
    opacity: 1;
}

.text-reveal {
    position: relative;
}

.text-reveal>.content {
    animation: text-reveal-clip 3s linear infinite;
}

.text-reveal::before {
    content: '';
    position: absolute;
    background: black;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    z-index: 99;

    animation: text-reveal-animation 3s linear infinite;
}

@keyframes text-reveal-clip {
    0% {
        clip-path: inset(0 100% 0 0);
    }


    40% {
        clip-path: inset(0 100% 0 0);
    }

    65% {
        clip-path: inset(0 0% 0 0);
    }


    80% {
        clip-path: inset(0 0% 0 0);
    }

    100% {
        clip-path: inset(0 100% 0 0);
    }
}

@keyframes text-reveal-animation {
    0% {
        opacity: 1;
    }

    5% {
        opacity: 0;
    }

    10% {
        opacity: 0;
    }

    15% {
        opacity: 1;
    }

    20% {
        opacity: 0;
    }

    25% {
        opacity: 1
    }

    30% {
        opacity: 0
    }

    35% {
        opacity: 1
    }

    40% {
        opacity: 0.5;
        left: 0%;
    }

    65% {
        opacity: 1;
        left: 100%;
    }


    70% {
        opacity: 0;
    }

    75% {
        opacity: 1;
    }

    80% {
        opacity: 0;
        left: 100%;
    }

    85% {
        opacity: 1
    }

    90% {
        opacity: 0;
    }

    95% {
        opacity: 1;
    }

    100% {
        left: 0%;
        opacity: 0;
    }
}